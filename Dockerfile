# # syntax=docker/dockerfile:1.4

# FROM node:18-alpine AS base
# WORKDIR /app
# RUN apk add --no-cache bash
# RUN npm install -g pnpm

# # Install dependencies
# COPY package.json pnpm-lock.yaml ./
# RUN pnpm install --frozen-lockfile

# # Copy source code and build
# COPY . .
# RUN pnpm build

# # Production stage - clean image with only runtime files
# FROM node:18-alpine AS production
# WORKDIR /app
# ENV NODE_ENV=production

# # Install pnpm in production image
# RUN npm install -g pnpm

# # Copy built application and dependencies
# COPY --from=base /app/.next ./.next
# COPY --from=base /app/public ./public
# COPY --from=base /app/node_modules ./node_modules
# COPY --from=base /app/next.config.ts /app/package.json /app/pnpm-lock.yaml ./

# EXPOSE 3000

# CMD ["pnpm", "start"]


# Enable BuildKit caching
# syntax=docker/dockerfile:1.4

############################
####### Base Stage ########
############################
FROM node:18-alpine AS base
WORKDIR /app
RUN apk add --no-cache bash
# install pnpm globally
RUN npm install -g pnpm

############################
####### Deps Stage ########
############################
FROM base AS deps
# only copy lockfiles to leverage caching
COPY package.json pnpm-lock.yaml ./
# install dependencies once, cache in this layer
RUN pnpm install --frozen-lockfile

############################
## Development Stage #######
############################
FROM deps AS development
# copy everything & expose for hot-reload
COPY . .
EXPOSE 3000
# run Next.js in dev mode
CMD ["pnpm", "dev"]

############################
####### Build Stage ########
############################
FROM deps AS builder
COPY . .
RUN pnpm build

############################
####### Prod Stage #########
############################
FROM node:18-alpine AS production
WORKDIR /app
ENV NODE_ENV=production
# copy only what runtime needs
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=deps    /app/node_modules ./node_modules
# use the actual config filename in your repo
COPY next.config.ts package.json pnpm-lock.yaml ./
EXPOSE 3000
CMD ["pnpm", "start"]





