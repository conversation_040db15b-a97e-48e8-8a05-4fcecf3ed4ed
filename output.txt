.
├── certificates
│   ├── localhost-key.pem
│   └── localhost.pem
├── components.json
├── docker-compose.yml
├── Dockerfile
├── eslint.config.mjs
├── next-env.d.ts
├── next.config.ts
├── out
│   ├── _next
│   │   ├── gm8RVMY2deGO7yx3ms8dI
│   │   └── static
│   │       ├── chunks
│   │       │   ├── 24-532eb31b0d693b7b.js
│   │       │   ├── 702-4b1f2bf71cc97f24.js
│   │       │   ├── 748859cf-d32b600725fd6325.js
│   │       │   ├── app
│   │       │   │   ├── _not-found
│   │       │   │   │   └── page-157eebbd542646b6.js
│   │       │   │   ├── layout-5471a944fa03c253.js
│   │       │   │   └── page-f6f7944e3ab3ed10.js
│   │       │   ├── framework-1666e5e1440943f9.js
│   │       │   ├── main-187dce047f7ec5c4.js
│   │       │   ├── main-app-4513d58bb9410653.js
│   │       │   ├── pages
│   │       │   │   ├── _app-6d3bfc6b1c1130b6.js
│   │       │   │   └── _error-9b0cabb989c1f02d.js
│   │       │   ├── polyfills-42372ed130431b0a.js
│   │       │   └── webpack-4fc29b61dcfb3273.js
│   │       ├── css
│   │       │   └── 774ce0ea16eb9128.css
│   │       └── gm8RVMY2deGO7yx3ms8dI
│   │           ├── _buildManifest.js
│   │           └── _ssgManifest.js
│   ├── 404.html
│   ├── favicon.ico
│   ├── file.svg
│   ├── globe.svg
│   ├── index.html
│   ├── index.txt
│   ├── next.svg
│   ├── vercel.svg
│   └── window.svg
├── output.txt
├── package-lock.json
├── package.json
├── pnpm-lock.yaml
├── postcss.config.js
├── postcss.config.mjs
├── public
│   ├── file.svg
│   ├── globe.svg
│   ├── logo.jpg
│   ├── next.svg
│   ├── vercel.svg
│   └── window.svg
├── README.md
├── src
│   ├── app
│   │   ├── favicon.ico
│   │   ├── globals.css
│   │   ├── head.tsx
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components
│   │   ├── ai
│   │   │   ├── AISummarizer.tsx
│   │   │   └── ChatBot.tsx
│   │   ├── auth
│   │   │   └── LoginModal.tsx
│   │   ├── charts
│   │   │   ├──  KeepaChart.jsx
│   │   │   ├── BuyBoxChart.tsx
│   │   │   ├── BuyBoxHistorySection.tsx
│   │   │   ├── KeepaSection.jsx
│   │   │   ├── OffersDistributionChart.tsx
│   │   │   └── PriceHistoryChart.tsx
│   │   ├── data
│   │   │   └── AverageDataSection.tsx
│   │   ├── ProfitCalculator.tsx
│   │   ├── settings
│   │   │   └── SettingsPanel.tsx
│   │   └── ui
│   │       ├── accordion.tsx
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── tabs.tsx
│   │       ├── text.tsx
│   │       ├── textarea.tsx
│   │       └── tooltip.tsx
│   ├── hooks
│   │   ├── useAuth.tsx
│   │   └── useLocalStorage.ts
│   ├── lib
│   │   ├── api
│   │   │   ├── productApi.ts
│   │   │   └── storage.ts
│   │   ├── auth
│   │   │   ├── api.ts
│   │   │   └── types.ts
│   │   ├── types
│   │   │   └── home.ts
│   │   ├── utils
│   │   │   ├── cookie.ts
│   │   │   ├── css.ts
│   │   │   └── index.ts
│   │   └── utils.ts
│   └── utils
│       └── extensionCommunication.ts
├── tailwind.config.js
├── tsconfig.json
├── tsconfig.server.json
└── yarn.lock

29 directories, 86 files
