"use client";
import AccordionData from "@/components/extension/accordion-data";
import Header from "@/components/extension/Header";
import Nav from "@/components/extension/Nav";
import Overview from "@/components/extension/overview";
import OverViewHeader from "@/components/extension/overview-header";
import SystemiseFulfilmentAd from "@/components/extension/systemise-fulfillments-ads";
import { useProductContext } from "@/hooks/useProduct";

const Home = () => {
  const { dataLoadingProgress } = useProductContext();
  return (
    <main className="flex flex-col h-screen max-h-screen overflow-scroll bg-gray-100">
      {dataLoadingProgress === 100 && (
        <div className="flex-shrink-0">
          <Nav />
          <Header />
          <OverViewHeader />
          <Overview />
          <AccordionData />
          <SystemiseFulfilmentAd />
        </div>
      )}
      {dataLoadingProgress !== 100 && (
        <div className="p-6 h-screen flex items-center justify-center relative">
          <h1 className="font-bold text-black/80 -mt-12 text-[1.6rem] tracking-wide animate-pulse">
            CLICKBUYDEALS
          </h1>
          <div className="absolute top-50 left-50 w-[90%] h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-green-500 transition-all duration-300"
              style={{ width: `${dataLoadingProgress}%` }}
            />
          </div>
        </div>
      )}
    </main>
  );
};

export default Home;
