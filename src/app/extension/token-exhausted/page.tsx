"use client";

import { AlertTriangle } from "lucide-react";
import Image from "next/image";

export default function Page() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      {/* Logo Section */}
      <div className="mb-8">
        {/* <Image
          src="/logo.png"
          alt="ClickBuy Logo"
          width={200}
          height={40}
          className="object-contain"
        /> */}
      </div>

      {/* Error Message Card */}
      <div className="bg-white p-6 rounded-lg shadow-md max-w-md w-full text-center">
        <div className="flex justify-center mb-4">
          <AlertTriangle className="h-12 w-12 text-yellow-500" />
        </div>
        <h1 className="text-xl font-semibold text-gray-800 mb-2">
          Token Exhaused
        </h1>
        <p className="text-gray-600 mb-6">
          Your token has expired. Please upgrade your plan to continue using our
          services.
        </p>
        <button
          onClick={() =>
            window.open("https://extension.clickbuydeals.com/", "_blank")
          }
          className="bg-black hover:bg-black/80 text-white px-4 py-2 rounded-md transition-colors"
        >
          Upgrade Now
        </button>
      </div>
    </div>
  );
}
