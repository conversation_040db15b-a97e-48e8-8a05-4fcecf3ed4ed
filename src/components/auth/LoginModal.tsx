import React, { useState } from "react";
import { X, Alert<PERSON><PERSON>cle, Lock, EyeOff, Eye, Loader2 } from "lucide-react";
import { $http } from "@/lib/https";
import { API_URL } from "@/lib/constant";
import { storageService } from "@/lib/api/storage";
import { AUTH_COOKIES } from "@/lib/utils/cookie";
import { useAuth } from "@/hooks/useAuth";

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function LoginModal({ isOpen, onClose }: LoginModalProps) {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { setUser, setToken, user } = useAuth();

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    setIsLoading(true);

    e.preventDefault();

    try {
      const formData = new FormData();
      formData.append("username", username);
      formData.append("password", password);
      const { data } = await $http.post(
        `${API_URL}/auth/api/v1/token`,
        formData,
      );

      const user: { email: string; isLoggedIn: boolean } = {
        email: username,
        isLoggedIn: true,
      };

      storageService.setItem(AUTH_COOKIES.ACCESS_TOKEN, data.access_token);
      storageService.setItem(AUTH_COOKIES.REFRESH_TOKEN, data.refresh_token);
      storageService.setItem(AUTH_COOKIES.USER, JSON.stringify(user));

      setUser(user);
      setToken(data.access_token);

      onClose();
    } catch (error: any) {
      setError(
        error?.response?.data?.detail ||
          error?.message ||
          "Invalid email or password. Please try again.",
      );
      return;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-lg w-full max-w-md mx-3 overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between bg-black text-white p-4">
          <h2 className="text-lg font-semibold">Login to ClickBuy</h2>
          <button onClick={onClose} className="text-white hover:text-gray-300">
            <X size={20} />
          </button>
        </div>

        {error && (
          <div className="bg-red-50 text-red-800 p-3 flex items-start gap-2">
            <AlertCircle
              size={18}
              className="text-red-600 mt-0.5 flex-shrink-0"
            />
            <span className="text-sm">{error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Email
              </label>
              <input
                id="email"
                type="email"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-black"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-black"
                  placeholder="Your password"
                  required
                />
                <button
                  type="button"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                />
                <label
                  htmlFor="remember-me"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Remember me
                </label>
              </div>
              <a href="#" className="text-sm text-gray-600 hover:text-black">
                Forgot password?
              </a>
            </div>

            <div>
              <button
                type="submit"
                className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                disabled={false}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="animate-spin -ml-1 mr-2 h-4 w-4" />
                    Logging in...
                  </>
                ) : (
                  "Login"
                )}
              </button>
            </div>
            <div className="text-center text-sm text-gray-600">
              Don't have an account?{" "}
              <button
                className="text-black hover:underline"
                onClick={() =>
                  window.open("https://extension.clickbuydeals.com/", "_blank")
                }
              >
                Register here
              </button>
            </div>
          </div>
        </form>

        <div className="px-4 py-3 bg-gray-50 text-center text-xs text-gray-500">
          <div className="flex items-center justify-center gap-1">
            <Lock size={12} />
            <span>Secure login powered by ClickBuy AI</span>
          </div>
        </div>
      </div>
    </div>
  );
}
