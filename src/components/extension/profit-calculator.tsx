import { useAuth } from "@/hooks/useAuth";
import { useProductContext } from "@/hooks/useProduct";
import {
  formatToCurrency,
  getCurrencySymbol,
  getCurrentBuyBoxPrice,
  getCurrentFBAFees,
  getCurrentProfit,
  getCurrentReferralFee,
  getCurrentROI,
  getCurrentTotalFees,
} from "@/lib/utils/index";
import { Box, Lock } from "lucide-react";
import { useState } from "react";

const ProfitCalculator = () => {
  const { user } = useAuth();
  const {
    productData,
    isVatRegistered,
    setIsVatRegistered,
    selectedCountry,
    vat,
    setVat,
  } = useProductContext();

  const handleVatRegisteredChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setIsVatRegistered(e.target.checked);
  };

  const handleVatPercentageChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setVat(Number(e.target.value));
  };

  const formatCurrency = (value: number | undefined | null): string => {
    if (value === undefined || value === null)
      return `${getCurrencySymbol(selectedCountry)}0.00`;
    return `${getCurrencySymbol(selectedCountry)}${value.toFixed(2)}`;
  };

  return (
    <div className="bg-white">
      <div className="p-2">
        {/* Profit and ROI - separate box, extreme left, bigger text */}
        <div className="bg-gray-100 border border-gray-400 rounded p-2 mb-2">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs font-medium">Profit:</span>
            <span
              className={`text-xs font-bold ${
                ((productData &&
                  getCurrentProfit({ isVatRegistered, productData })) ||
                  0) > 0
                  ? "text-green-600"
                  : "text-red-600"
              }`}
            >
              {!productData ? (
                <div className="inline-block h-3 w-3 animate-spin rounded-full border border-gray-500 border-t-transparent"></div>
              ) : (
                formatCurrency(
                  getCurrentProfit({ isVatRegistered, productData }) || 0,
                )
              )}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs font-medium">ROI:</span>
            <span
              className={`text-xs font-bold ${
                ((productData &&
                  getCurrentROI({ isVatRegistered, productData })) ||
                  0) > 0
                  ? "text-green-600"
                  : "text-red-600"
              }`}
            >
              {!productData ? (
                <div className="inline-block h-3 w-3 animate-spin rounded-full border border-gray-500 border-t-transparent"></div>
              ) : (
                `${getCurrentROI({ isVatRegistered, productData })?.toFixed(1) || "0.0"}%`
              )}
            </span>
          </div>
        </div>

        {/* All breakdown including GST/VAT and Total Fees - in one separate box */}
        <div className="bg-gray-100 border border-gray-400 rounded p-2">
          {/* GST/VAT section inside the box */}
          <div className="flex items-center gap-2 mb-2 pb-2 border-b border-gray-300">
            {!user ? (
              <button className="flex items-center bg-white border px-1.5 py-0.5 rounded text-xs">
                <Lock size={10} className="mr-0.5" />
              </button>
            ) : (
              <input
                type="checkbox"
                checked={isVatRegistered || false}
                onChange={handleVatRegisteredChange}
              />
            )}
            <span className="text-xs">GST/VAT Registered</span>
            {!user ? (
              <button className="flex items-center bg-white border px-1.5 py-0.5 rounded text-xs">
                <Lock size={10} className="mr-0.5" />
              </button>
            ) : (
              <input
                type="number"
                value={vat}
                onChange={handleVatPercentageChange}
                className="w-12 text-xs border rounded px-1"
              />
            )}
            <span className="text-xs">GST/VAT %</span>
          </div>

          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span>Amazon Price:</span>
              <span>
                {productData &&
                  formatToCurrency(
                    getCurrentBuyBoxPrice({ isVatRegistered, productData }) ||
                      0,
                    selectedCountry,
                  )}
              </span>
            </div>
            <div className="flex justify-between">
              <span>FBA Fee:</span>
              <span className="text-red-600">
                {productData &&
                  formatToCurrency(
                    getCurrentFBAFees({ isVatRegistered, productData }) || 0,
                    selectedCountry,
                  )}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Referral Fee:</span>
              <span className="text-red-600">
                {productData &&
                  formatToCurrency(
                    getCurrentReferralFee({ isVatRegistered, productData }) ||
                      0,
                    selectedCountry,
                  )}
              </span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Total Fees:</span>
              <span className="text-red-600">
                {productData &&
                  formatToCurrency(
                    getCurrentTotalFees({ isVatRegistered, productData }) || 0,
                    selectedCountry,
                  )}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfitCalculator;
