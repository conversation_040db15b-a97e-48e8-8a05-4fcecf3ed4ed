"use client";
import { ExternalLink, Package, Settings } from "lucide-react";
import Image from "next/image";
import { Button } from "../ui/button";

const SystemiseFulfilmentAd = () => {
  return (
    <div className="max-w-4xl  bg-white border m-1 border-black p-1">
      {/* Header with logo */}
      <div className="bg-black text-white p-2 mb-8 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Image
            src="https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/3165569/settings_images/6c07625-c1a0-e52c-545a-cae5828ba56d_26332207-9158-4b68-8f9c-868813070265.png"
            alt="Systemise Logo"
            width={150}
            height={100}
            className="object-contain"
          />
        </div>
      </div>

      {/* Main content */}
      <div className="text-center space-y-6">
        {/* Main headline */}
        <p className="text-sm font-bold text-gray-900 ">
          Your Trusted eCommerce Fulfilment & Logistics Partner 🚀
        </p>

        {/* Key benefits */}
        <div className=" text-xs text-gray-800 !mt-1">
          <p>Managing your warehouse order fulfilment effortless.</p>
          <p>Managing your warehouse preparation efficiently.</p>
        </div>

        {/* Perfect for section */}
        <div className="!py-2 !mt-0">
          <p className="text-xs text-gray-900 leading-relaxed">
            Perfect For Amazon Business, Brands, eCommerce Business, B2C, B2B,
          </p>
          <p className="text-xs text-gray-900 leading-relaxed">
            3PL, eBay, FBM, FBA Own Website, Books, TikTok, Etsy, B&Q & Much
            More.
          </p>
        </div>

        {/* Warehouses location */}

        {/* CTA Button */}
        <div className="pb-6 !mt-1">
          <p className="text-[9px] text-gray-800 py-0 !mt-0 mb-1">
            Warehouses in UK, USA & Europe.
          </p>
          <form
            action={async () => {
              window.open(
                "https://www.systemisefulfilment.co.uk/a/2147974982/QyaKRbHz",
                "_blank",
              );
            }}
          >
            <Button className="bg-black text-white px-4 gap-2 py-2 text-md font-bold hover:bg-gray-800 transition-colors duration-200 flex items-center justify-between mx-auto space-x-3 w-fit">
              <span>Get Started Here</span>
              <ExternalLink className="w-6 h-6" />
            </Button>
          </form>
          <p className="text-[9px] text-gray-800 py-0 !mt-1 mb-1">
            Join other sellers already using Systemise Fulfilment.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SystemiseFulfilmentAd;
