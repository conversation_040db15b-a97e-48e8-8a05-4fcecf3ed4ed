import React from "react";
import { createRoot } from "react-dom/client";

// Modal component with blur effect
interface ModalProps {
  message: React.ReactNode;
}

const Modal = ({ message }: ModalProps) => {
  return (
    <div className="fixed inset-0 z-[1004] flex items-center justify-center">
      <div className="absolute inset-0 bg-back/30 backdrop-blur-sm" />
      <div className="relative z-10 w-[96%] max-w-md bg-white border border-gray-400 p-6 rounded-lg shadow-lg animate-fade-in">
        <div className="flex flex-col">
          <div className="flex-1 text-red-500 text-sm mt-2">{message}</div>
        </div>
      </div>
    </div>
  );
};

export const tokenErrorToast = async (response: Response) => {
  try {
    const errorData = await response.json();

    const root = document.createElement("div");
    root.id = "modal-" + Date.now();
    document.body.appendChild(root);

    // Function to clean up the modal
    const cleanup = () => {
      if (root && root.parentNode) {
        root.parentNode.removeChild(root);
      }
    };

    // Create the modal message with JSX
    const message = (
      <>
        <div className="mb-4">
          <div className="flex justify-center">
            <span className="text-center">
              {errorData.detail ||
                "You've reached your daily limit. Please upgrade your plan to continue."}
            </span>
          </div>
        </div>
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={() =>
              window.open("https://extension.clickbuydeals.com/", "_blank")
            }
            className="bg-black hover:bg-black/80 text-white px-4 py-2 rounded-md transition-colors"
          >
            Upgrade Now
          </button>
        </div>
      </>
    );

    // Create and render the modal
    const modal = React.createElement(Modal, {
      message: message,
    });

    const rootElement = createRoot(root);
    rootElement.render(modal);
  } catch (error) {
    console.error("Error showing modal:", error);
    // Fallback to basic alert if React rendering fails
    alert(
      "You've reached your daily limit. Please upgrade your plan to continue.",
    );
  }
};
