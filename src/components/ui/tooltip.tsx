"use client";

import React, { useState } from "react";

interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  position?: "top" | "bottom" | "left" | "right";
  width?: string;
  maxHeight?: string;
  className?: string;
}

export function Tooltip({
  content,
  children,
  position = "top",
  width = "200px",
  maxHeight = "none",
  className = "",
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);

  const positions = {
    top: "bottom-full left-1/2 -translate-x-1/2 mb-2",
    bottom: "top-full left-1/2 -translate-x-1/2 mt-2",
    left: "right-full top-1/2 -translate-y-1/2 mr-2",
    right: "left-full top-1/2 -translate-y-1/2 ml-2",
  };

  return (
    <div className="relative inline-block tooltip-trigger">
      <div
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onFocus={() => setIsVisible(true)}
        onBlur={() => setIsVisible(false)}
      >
        {children}
      </div>
      <div
        className={`
          absolute z-10 rounded bg-black p-3 text-white opacity-0 transition-opacity duration-300
          ${positions[position]} ${isVisible ? "visible opacity-100" : "invisible"}
          ${className}
        `}
        style={{
          width,
          maxHeight: maxHeight !== "none" ? maxHeight : undefined,
          overflowY: maxHeight !== "none" ? "auto" : undefined,
        }}
      >
        {content}
      </div>
    </div>
  );
}
