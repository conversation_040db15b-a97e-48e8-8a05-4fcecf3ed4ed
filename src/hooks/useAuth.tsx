"use client";
import React, { createContext, useContext, useState, ReactNode } from "react";
import { User, AuthTokens } from "../lib/auth/types";
import { AUTH_COOKIES } from "../lib/utils/cookie";
import { storageService } from "../lib/api/storage";

interface AuthContextType {
  user: User | null;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  token: AuthTokens | null;
  setToken: React.Dispatch<React.SetStateAction<AuthTokens | null>>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(() => {
    const userJson = storageService.getItem(AUTH_COOKIES.USER);
    return userJson ? JSON.parse(userJson) : null;
  });

  const [token, setToken] = useState<AuthTokens | null>(() => {
    const accessToken = storageService.getItem(AUTH_COOKIES.ACCESS_TOKEN);
    const refreshToken = storageService.getItem(AUTH_COOKIES.REFRESH_TOKEN);
    return accessToken && refreshToken
      ? {
          access_token: accessToken,
          refresh_token: refreshToken,
          token_type: "Bearer",
        }
      : null;
  });

  return (
    <AuthContext.Provider value={{ user, setUser, token, setToken }}>
      {children}
    </AuthContext.Provider>
  );
};
