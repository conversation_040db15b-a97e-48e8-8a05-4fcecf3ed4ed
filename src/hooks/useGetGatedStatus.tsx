import { $http } from "@/lib/https";
import { useQuery } from "@tanstack/react-query";
import { getAccessToken } from "../lib/utils/index";
import { useAuth } from "./useAuth";

interface GatedRequest {
  asin_code: string;
  countryCode: string;
  fullResponse?: boolean;
}

export const useGetGatedStatus = ({
  GatedRequest,
}: {
  GatedRequest: GatedRequest;
}) => {
  const { user } = useAuth();
  const queryKey = [`gated-status-${GatedRequest?.asin_code}`, user];
  const accessToken = getAccessToken();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await $http.get(
        `/extension-backend/api/v1/amazon/gated/${GatedRequest?.asin_code}?country_code=${GatedRequest?.countryCode}`,
        {
          headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );
      return response.data;
    },
    enabled:
      !!GatedRequest.asin_code && !!user && GatedRequest.fullResponse !== false,
  });
};
