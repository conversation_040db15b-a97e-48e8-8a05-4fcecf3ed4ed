import { $http } from "@/lib/https";
import { useQuery } from "@tanstack/react-query";
import { getAccessToken } from "../lib/utils/index";
import { useAuth } from "./useAuth";

interface KeepaRequest {
  asin: string;
  country: string;
  vat: number;
  sellerPrice: number;
  fullResponse?: boolean;
}

export const useGetKeepaData = ({
  KeepaRequest,
}: {
  KeepaRequest: KeepaRequest;
}) => {
  const { user } = useAuth();
  const queryKey = [
    `keepa-data-${KeepaRequest?.asin}`,
    user,
    KeepaRequest.fullResponse,
  ];
  const accessToken = getAccessToken();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await $http.get(
        `/extension-backend/api/v1/kepa/get-keepa-product/${KeepaRequest.asin}?country=${KeepaRequest.country}&vat=${KeepaRequest.vat}&seller_price=${KeepaRequest.sellerPrice}&full_response=${KeepaRequest.fullResponse || false}`,
        {
          headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );
      return response.data;
    },
    enabled:
      !!KeepaRequest.asin &&
      !!user &&
      !!KeepaRequest.vat &&
      !!KeepaRequest.sellerPrice,
  });
};
