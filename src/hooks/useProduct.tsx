"use client";

import {
  extractAsinFromUrl,
  parseAmazonProductPage,
} from "@/lib/api/productApi";
import { ProductData } from "@/lib/types/home";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
  useState,
} from "react";
import { useGetKeepaData } from "./useGetKeepaData";
import { getCurrentBuyBoxPrice } from "../lib/utils/index";
import { useGetGatedStatus } from "./useGetGatedStatus";
import { useProfitCalculator } from "./useProfitCalculator";
import { useParams } from "next/navigation";

interface DataContextType {
  productData: ProductData | null;
  setProductData: React.Dispatch<React.SetStateAction<ProductData | null>>;
  selectedCountry: "GB" | "US";
  setSelectedCountry: React.Dispatch<React.SetStateAction<"GB" | "US">>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  dataLoadingProgress: number;
  setDataLoadingProgress: React.Dispatch<React.SetStateAction<number>>;
  pageUrl: string | null;
  setPageUrl: React.Dispatch<React.SetStateAction<string | null>>;
  isVatRegistered: boolean;
  setIsVatRegistered: React.Dispatch<React.SetStateAction<boolean>>;
  isGated: boolean | undefined;
  setIsGated: React.Dispatch<React.SetStateAction<boolean | undefined>>;
  vat: number;
  setVat: React.Dispatch<React.SetStateAction<number>>;
  inputBuyBoxPrice: string;
  setInputBuyBoxPrice: React.Dispatch<React.SetStateAction<string>>;
  inputPurchasePrice: string;
  setInputPurchasePrice: React.Dispatch<React.SetStateAction<string>>;
  fullResponse: boolean;
  setFullResponse: React.Dispatch<React.SetStateAction<boolean>>;
  profitLoading: boolean;
  hasNotProfitLoaded: boolean;
}

const ProductContext = createContext<DataContextType | undefined>(undefined);

export const ProductProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [productData, setProductData] = useState<ProductData | null>(null);
  const [selectedCountry, setSelectedCountry] = useState<"GB" | "US">("GB");
  const [isLoading, setIsLoading] = useState(true);
  const [dataLoadingProgress, setDataLoadingProgress] = useState(0);
  const [pageUrl, setPageUrl] = useState<string | null>(null);
  const [isVatRegistered, setIsVatRegistered] = useState(false);
  const [isGated, setIsGated] = useState<boolean | undefined>(undefined);
  const [vat, setVat] = useState<number>(20);
  const [inputBuyBoxPrice, setInputBuyBoxPrice] = useState<string>("0");
  const [inputPurchasePrice, setInputPurchasePrice] = useState("");
  const [fullResponse, setFullResponse] = useState(false);
  const [hasNotProfitLoaded, setProfitNotLoaded] = useState(true);

  const { data: quickData } = useGetKeepaData({
    KeepaRequest: {
      asin: productData?.asin || "",
      country: selectedCountry || "GB",
      vat: vat,
      sellerPrice:
        (productData &&
          getCurrentBuyBoxPrice({
            isVatRegistered: isVatRegistered,
            productData: productData,
          })) ||
        0,
      fullResponse: false,
    },
  });

  const { data: gated } = useGetGatedStatus({
    GatedRequest: {
      asin_code: productData?.asin || "",
      countryCode: selectedCountry || "GB",
      fullResponse: fullResponse,
    },
  });

  const { data: fullData } = useGetKeepaData({
    KeepaRequest: {
      asin: productData?.asin || "",
      country: selectedCountry || "GB",
      vat: vat,
      sellerPrice:
        (productData &&
          getCurrentBuyBoxPrice({
            isVatRegistered: isVatRegistered,
            productData: productData,
          })) ||
        0,
      fullResponse: productData?.dimensions?.height.value !== 0 ? true : false,
    },
  });

  const { data: profitData, isLoading: profitLoading } = useProfitCalculator({
    ProfitCalcRequest: {
      fba_fees: fullResponse
        ? isVatRegistered
          ? productData?.pricing?.vat_pricing?.fba_fee || 0
          : productData?.pricing?.non_vat_pricing?.fba_fee || 0
        : productData?.pricing?.metrics?.fba_fees || 0,
      referral_percent: fullResponse
        ? isVatRegistered
          ? productData?.pricing?.vat_pricing?.referral_fee || 0
          : productData?.pricing?.non_vat_pricing?.referral_fee || 0
        : productData?.pricing?.metrics?.referral_percent_real || 0,
      variable_closing_fee: fullResponse
        ? isVatRegistered
          ? productData?.pricing?.vat_pricing?.variable_closing_fee || 0
          : productData?.pricing?.non_vat_pricing?.variable_closing_fee || 0
        : productData?.pricing?.metrics?.variable_closing_fee_real || 0,
      buy_box_price: Number(inputBuyBoxPrice),
      seller_price: Number(inputBuyBoxPrice),
      sales_rank: fullData?.pricing?.sales_rank || 0,
      country: selectedCountry,
      vat: isVatRegistered ? vat : 0,
      asin_code: productData?.asin || "",
      fullResponse: fullData && fullResponse,
    },
  });

  useEffect(() => {
    if (profitData) {
      setProductData((prevState) => ({
        ...prevState,
        pricing: {
          ...prevState?.pricing,
          non_vat_pricing: {
            ...prevState?.pricing?.non_vat_pricing,
            referral_fee:
              prevState?.pricing?.non_vat_pricing?.referral_fee || 0,
            variable_closing_fee:
              prevState?.pricing?.non_vat_pricing?.variable_closing_fee || 0,
            seller_price:
              prevState?.pricing?.non_vat_pricing?.seller_price || 0,
            buy_box_price:
              prevState?.pricing?.non_vat_pricing?.buy_box_price || 0,
            fba_fee: prevState?.pricing?.non_vat_pricing?.fba_fee || 0,
            sales_rank: prevState?.pricing?.non_vat_pricing?.sales_rank || 0,
            // update below
            roi: profitData.not_vat_registed.fees.roi || 0,
            total_fee: profitData.not_vat_registed.fees.total_fee || 0,
            profit: profitData.not_vat_registed.fees.profit || 0,
          },
          vat_pricing: {
            ...prevState?.pricing?.vat_pricing,
            referral_fee: prevState?.pricing?.vat_pricing?.referral_fee || 0,
            variable_closing_fee:
              prevState?.pricing?.vat_pricing?.variable_closing_fee || 0,
            seller_price: prevState?.pricing?.vat_pricing?.seller_price || 0,
            buy_box_price: prevState?.pricing?.vat_pricing?.buy_box_price || 0,
            fba_fee: prevState?.pricing?.vat_pricing?.fba_fee || 0,
            sales_rank: prevState?.pricing?.vat_pricing?.sales_rank || 0,
            // update below
            roi: profitData.vat_registed.fees.roi || 0,
            total_fee: profitData.vat_registed.fees.total_fee || 0,
            profit: profitData.vat_registed.fees.profit || 0,
          },
        },
      }));
      setProfitNotLoaded(false);
    }
  }, [profitData]);

  useEffect(() => {
    if (gated && gated.restrictions && gated.restrictions.length > 0) {
      setIsGated(true);
    } else {
      setIsGated(false);
    }
  }, [gated]);

  useEffect(() => {
    if (quickData) {
      setProductData((prevState) => ({
        ...prevState,
        mainImage: quickData.summary.image_url || prevState?.mainImage || "",
        title: quickData.summary.title || prevState?.title || "",
        brand: quickData.summary.brand || prevState?.brand || "",
        category: quickData.summary.category || prevState?.category || "",
        rating: quickData.summary.rating?.toString() || prevState?.rating || "",
        reviewCount:
          quickData.summary.reviews?.toString() || prevState?.reviewCount || "",
        ean:
          quickData.summary && Array.isArray(quickData.summary.ean)
            ? quickData.summary.ean[0]
            : prevState?.ean || "",
        features: quickData.summary.features || prevState?.features || [],
        estimated_sales:
          quickData.summary.monthly_sold || prevState?.estimated_sales || 0,
        pricing: {
          non_vat_pricing: {
            buy_box_price:
              prevState?.pricing?.non_vat_pricing?.buy_box_price || 0,
            fba_fee: quickData.pricing.live_analysis.metrics.fba_fees_real || 0,
            profit:
              quickData.pricing.live_analysis.not_vat_registed.fees.profit || 0,
            referral_fee:
              quickData.pricing.referral_fee.referral_fee_percentage || 0,
            roi: quickData.pricing.live_analysis.not_vat_registed.fees.roi || 0,
            sales_rank: quickData.pricing.sales_rank || 0,
            seller_price:
              quickData.pricing.live_analysis.metrics.seller_price_real || 0,
            total_fee:
              quickData.pricing.live_analysis.not_vat_registed.fees
                .total_fees || 0,
            variable_closing_fee:
              quickData.pricing.live_analysis.metrics
                .variable_closing_fee_real || 0,
          },
          vat_pricing: {
            buy_box_price:
              prevState?.pricing?.non_vat_pricing?.buy_box_price || 0,
            fba_fee: quickData.pricing.live_analysis.metrics.fba_fees || 0,
            profit:
              quickData.pricing.live_analysis.vat_registed.fees.profit || 0,
            referral_fee:
              quickData.pricing.referral_fee.referral_fee_percent || 0,
            roi: quickData.pricing.live_analysis.vat_registed.fees.roi || 0,
            sales_rank: quickData.pricing.sales_rank || 0,
            seller_price:
              quickData.pricing.live_analysis.metrics.seller_price || 0,
            total_fee:
              quickData.pricing.live_analysis.vat_registed.fees.total_fees || 0,
            variable_closing_fee:
              quickData.pricing.live_analysis.metrics.variable_closing_fee || 0,
          },
        },
        offers: {
          total: quickData.offers.offers || 0,
          fba: quickData.offers.fba || 0,
          fbm: quickData.offers.fbm || 0,
          sellers_offers: quickData.offers.sellers_offers || [],
        },
        warnings: {
          adult_product: quickData.warnings.adult_product || false,
          amz_in_buy_box: quickData.warnings.amz_in_buy_box || false,
          meltable: quickData.warnings.meltable || false,
          variations: quickData.warnings.variations || undefined,
        },
        dimensions: quickData.dimensions || undefined,
        graph_quickData: quickData.graph_quickData || undefined,
        rank_and_price_history: quickData.rank_and_price_history || undefined,
        summary: {
          title: quickData.summary.title || prevState?.summary?.title || "",
          brand: quickData.summary.brand || prevState?.summary?.brand || "",
          category:
            quickData.summary.category || prevState?.summary?.category || "",
          rating:
            quickData.summary.rating?.toString() ||
            prevState?.summary?.rating ||
            "",
          reviews:
            quickData.summary.reviews?.toString() ||
            prevState?.summary?.reviews ||
            "",
          features:
            quickData.summary.features || prevState?.summary?.features || [],
          ean:
            quickData.summary.ean && Array.isArray(quickData.summary.ean)
              ? quickData.summary.ean[0]
              : prevState?.summary?.ean || "",
          google_url:
            quickData.summary.google_url ||
            prevState?.summary?.google_url ||
            "",
          hagglezon_url:
            quickData.summary.hagglezon_url ||
            prevState?.summary?.hagglezon_url ||
            "",
          euro_search_url:
            quickData.summary.euro_search_url ||
            prevState?.summary?.euro_search_url ||
            "",
          ebay_active_listing_url:
            quickData?.summary?.ebay_active_listing_url || "",
          ebay_sold_listing_url:
            quickData?.summary?.ebay_sold_listing_url || "",
          bsr: quickData?.summary?.bsr || "",
        },
        buy_box_history: quickData.buy_box_history || undefined,
      }));
    }
  }, [quickData]);

  useEffect(() => {
    if (fullData) {
      setProductData((prevState) => ({
        ...prevState,
        summary: {
          title: fullData.summary.title || prevState?.summary?.title || "",
          brand: fullData.summary.brand || prevState?.summary?.brand || "",
          category:
            fullData.summary.category || prevState?.summary?.category || "",
          rating:
            fullData.summary.rating?.toString() ||
            prevState?.summary?.rating ||
            "",
          reviews:
            fullData.summary.reviews?.toString() ||
            prevState?.summary?.reviews ||
            "",
          features:
            fullData.summary.features || prevState?.summary?.features || [],
          ean:
            fullData.summary.ean && Array.isArray(fullData.summary.ean)
              ? fullData.summary.ean[0]
              : prevState?.summary?.ean || "",
          google_url:
            fullData.summary.google_url || prevState?.summary?.google_url || "",
          hagglezon_url:
            fullData.summary.hagglezon_url ||
            prevState?.summary?.hagglezon_url ||
            "",
          euro_search_url:
            fullData.summary.euro_search_url ||
            prevState?.summary?.euro_search_url ||
            "",
          ebay_active_listing_url:
            fullData?.summary?.ebay_active_listing_url || "",
          ebay_sold_listing_url: fullData?.summary?.ebay_sold_listing_url || "",
          bsr: fullData?.summary?.bsr || "",
        },
        pricing: {
          sales_rank: fullData.pricing.sales_rank || 0,
          non_vat_pricing: {
            buy_box_price:
              prevState?.pricing?.non_vat_pricing?.buy_box_price || 0,
            fba_fee: fullData.pricing.live_analysis.metrics.fba_fees_real || 0,
            profit:
              fullData.pricing.live_analysis.not_vat_registed.fees.profit || 0,
            referral_fee:
              fullData.pricing.referral_fee.referral_fee_percentage || 0,
            roi: fullData.pricing.live_analysis.not_vat_registed.fees.roi || 0,
            sales_rank: fullData.pricing.sales_rank || 0,
            seller_price:
              fullData.pricing.live_analysis.metrics.seller_price_real || 0,
            total_fee:
              fullData.pricing.live_analysis.not_vat_registed.fees.total_fees ||
              0,
            variable_closing_fee:
              fullData.pricing.live_analysis.metrics
                .variable_closing_fee_real || 0,
          },
          vat_pricing: {
            buy_box_price:
              prevState?.pricing?.non_vat_pricing?.buy_box_price || 0,
            fba_fee: fullData.pricing.live_analysis.metrics.fba_fees || 0,
            profit:
              fullData.pricing.live_analysis.vat_registed.fees.profit || 0,
            referral_fee:
              fullData.pricing.referral_fee.referral_fee_percent || 0,
            roi: fullData.pricing.live_analysis.vat_registed.fees.roi || 0,
            sales_rank: fullData.pricing.sales_rank || 0,
            seller_price:
              fullData.pricing.live_analysis.metrics.seller_price || 0,
            total_fee:
              fullData.pricing.live_analysis.vat_registed.fees.total_fees || 0,
            variable_closing_fee:
              fullData.pricing.live_analysis.metrics.variable_closing_fee || 0,
          },
        },
        offers: {
          total: fullData.offers.offers || 0,
          fba: fullData.offers.fba || 0,
          fbm: fullData.offers.fbm || 0,
          sellers_offers: fullData.offers.sellers_offers || [],
        },
        warnings: {
          adult_product: fullData.warnings.adult_product || false,
          amz_in_buy_box: fullData.warnings.amz_in_buy_box || false,
          meltable: fullData.warnings.meltable || false,
          variations: fullData.warnings.variations || undefined,
        },
        dimensions: fullData.dimensions || undefined,
        graph_data: fullData.graph_data || undefined,
        rank_and_price_history: fullData.rank_and_price_history || undefined,
        buy_box_history: fullData.buy_box_history || undefined,
      }));
      setFullResponse(true);
    }
  }, [fullData]);

  useEffect(() => {
    const price = productData?.price?.replace(/[^0-9.]/g, "") || "";
    setInputBuyBoxPrice(price);
    const randomMultiplier = 0.95 + Math.random() * 0.1;
    const purchasePrice = (parseFloat(price) * randomMultiplier).toFixed(2);
    setInputPurchasePrice(purchasePrice);
  }, [productData?.price]);

  const handleMessage = useCallback((event: MessageEvent) => {
    if (event.data.data.url) {
      if (event.data.data.url.includes(".com")) {
        setSelectedCountry("US");
      } else if (event.data.data.url.includes(".co.uk")) {
        setSelectedCountry("GB");
      }
    }

    console.log("Received message from extension:", event.data);
    if (event.data && event.data.type === "productData") {
      try {
        const data = event.data.data;
        const stage = event.data.stage || "unknown";

        if (isLoading) {
          setIsLoading(false);
        }

        // Update loading progress based on stage
        if (dataLoadingProgress < 100) {
          if (stage === "basic") {
            setDataLoadingProgress(25);
          } else if (stage === "medium") {
            setDataLoadingProgress(50);
          } else if (stage === "complete") {
            // Add a slight delay before reaching 100%
            setDataLoadingProgress(75);
            setTimeout(() => {
              setDataLoadingProgress(100);
            }, 1000);
          }
        }

        setProductData((prevState) => {
          if (!prevState) {
            return data;
          }

          return {
            ...prevState,
            ...data,
          };
        });
      } catch (err) {
        console.error("Error processing product data message:", err);
      }
    }
  }, []);

  // Effect for setting up message listeners
  useEffect(() => {
    window.addEventListener("message", handleMessage);

    // Signal to the parent that we're ready to receive data - BUT ONLY ONCE
    if (window.parent) {
      try {
        window.parent.postMessage({ type: "ready", debug: Date.now() }, "*");
      } catch (err) {
        console.error("Error sending ready message:", err);
        setIsLoading(false);
      }
    }

    // Cleanup listener on unmount
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [handleMessage]);

  console.log("selectedCountry", selectedCountry);

  return (
    <ProductContext.Provider
      value={{
        productData,
        setProductData,
        selectedCountry,
        setSelectedCountry,
        isLoading,
        setIsLoading,
        dataLoadingProgress,
        setDataLoadingProgress,
        pageUrl,
        setPageUrl,
        isVatRegistered,
        setIsVatRegistered,
        isGated,
        setIsGated,
        vat,
        setVat,
        inputBuyBoxPrice,
        setInputBuyBoxPrice,
        inputPurchasePrice,
        setInputPurchasePrice,
        fullResponse,
        setFullResponse,
        profitLoading,
        hasNotProfitLoaded,
      }}
    >
      {children}
    </ProductContext.Provider>
  );
};

export const useProductContext = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error("useProductContext must be used within a ProductProvider");
  }
  return context;
};
