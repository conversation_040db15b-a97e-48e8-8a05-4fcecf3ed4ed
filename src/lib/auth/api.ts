import { AuthTokens, LoginCredentials } from "./types";

const AUTH_API_URL = "https://app.clickbuy.ai/auth/api/v1";

/**
 * Login to the API and get authentication tokens
 */
export async function loginUser(
  credentials: LoginCredentials,
): Promise<AuthTokens> {
  try {
    const formData = new URLSearchParams();
    formData.append("username", credentials.username);
    formData.append("password", credentials.password);

    const response = await fetch(`${AUTH_API_URL}/token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
      body: formData.toString(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to login");
    }

    const data = await response.json();
    const tokens = data as AuthTokens;

    // Print the access token when received
    //console.log("Access token received:", tokens.access_token);

    return tokens;
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
}

/**
 * Refresh the access token using the refresh token
 */
export async function refreshToken(refreshToken: string): Promise<AuthTokens> {
  try {
    const formData = new URLSearchParams();
    formData.append("grant_type", "refresh_token");
    formData.append("refresh_token", refreshToken);

    const response = await fetch(`${AUTH_API_URL}/refresh`, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
      body: formData.toString(),
    });

    if (!response.ok) {
      throw new Error("Failed to refresh token");
    }

    const data = await response.json();
    const tokens = data as AuthTokens;

    // Also print the new access token when refreshed

    return tokens;
  } catch (error) {
    console.error("Token refresh error:", error);
    throw error;
  }
}

/**
 * Logout the user by clearing tokens
 */
export async function logoutUser(): Promise<void> {
  // This is a client-side logout - we'll just clear the tokens from storage
  return Promise.resolve();
}
