import axios, { AxiosInstance } from "axios";
import { API_URL, APP_URL } from "./constant";

const $http = axios.create({
  baseURL: API_URL,
  headers: {
    "Access-Control-Allow-Origin": "*",
  },
});

$http.interceptors.request.use(
  (response) => response,
  (error) => {
    return Promise.reject(error);
  },
);

const addAccessTokenToHttpInstance = (token: string) => {
  $http.interceptors.request.use(
    (config) => {
      config.headers["Authorization"] = `Bearer ${token}`;
      return config;
    },
    (error) => {
      return Promise.reject(error);
    },
  );
};

export { $http, addAccessTokenToHttpInstance };
