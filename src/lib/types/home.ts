export interface VariationAttributes {
  dimension: string;
  value: string;
}

export interface VariationData {
  asin: string;
  attributes: VariationAttributes[];
}

export interface RankPriceData {
  new?: { price: number; domain: string };
  sales_rank?: number;
  rating?: number;
  reviews?: number;
  offers?: number;
  buy_box_price?: { price: number; domain: string };
  fbm?: number;
  fba?: number;
  sales_rank_drop?: number;
}

export interface OfferChartItem {
  name: string;
  value: number;
  color: string;
}

export interface ProductData {
  title?: string;
  roi?: string;
  asin?: string;
  price?: string;
  rating?: string;
  reviewCount?: string;
  brand?: string;
  ean?: string;
  mainImage?: string;
  category?: string;
  features?: string[];
  bsr?: string;
  estimated_sales?: number;
  url?: string;
  summary?: {
    title: string;
    brand: string;
    category: string;
    rating: string;
    reviews: string;
    features: string[];
    ean: string;
    google_url: string;
    hagglezon_url: string;
    euro_search_url: string;
    ebay_active_listing_url: string;
    ebay_sold_listing_url: string;
    bsr: string;
  };
  pricing?: {
    sales_rank?: number;
    non_vat_pricing: {
      buy_box_price: number;
      fba_fee: number;
      profit: number;
      referral_fee: number;
      roi: number;
      sales_rank: number;
      seller_price: number;
      total_fee: number;
      variable_closing_fee: number;
    };
    vat_pricing?: {
      buy_box_price: number;
      fba_fee: number;
      profit: number;
      referral_fee: number;
      roi: number;
      sales_rank: number;
      seller_price: number;
      total_fee: number;
      variable_closing_fee: number;
    };
    metrics?: {
      fba_fees_real: number;
      fba_fees: number;
      seller_price_real: number;
      seller_price: number;
      variable_closing_fee_real: number;
      variable_closing_fee: number;
      referral_percent_real: number;
    };
  };
  offers?: {
    total: number;
    fba: number;
    fbm: number;
    sellers_offers?: Array<{
      seller_id: string;
      price: number;
      stock: number;
      is_fba: boolean;
      is_amazon: boolean;
    }>;
  };
  warnings?: {
    adult_product: boolean;
    amz_in_buy_box: boolean;
    meltable: boolean;
    variations?: {
      is_variations: boolean;
      variations_value: VariationData[];
    };
  };
  dimensions?: {
    height: { value: number; unit: string };
    length: { value: number; unit: string };
    width: { value: number; unit: string };
    weight: { value: number; unit: string };
  };
  graph_data?: {
    amazon: { time: string[]; data: (number | null)[] };
    new: { time: string[]; data: (number | null)[] };
    sales_rank: { time: string[]; data: (number | null)[] };
    fbm: { time: string[]; data: (number | null)[] };
    fba: { time: string[]; data: (number | null)[] };
    buy_box: { time: string[]; data: (number | null)[] };
  };
  rank_and_price_history?: {
    current: RankPriceData;
    avg: RankPriceData;
    avg30: RankPriceData;
    avg90: RankPriceData;
    avg180: RankPriceData;
    avg365: RankPriceData;
  };
  buy_box_history?: {
    "30_days": Record<string, number>;
    "90_days": Record<string, number>;
    "180_days": Record<string, number>;
    "365_days": Record<string, number>;
  };
}

// export interface for offer data
export interface OfferItem {
  position: number;
  type: "FBA" | "FBM" | "AMZ"; // Added 'AMZ' type
  stock: number;
  price: string;
}

// export interface for profit data
export interface ProfitData {
  not_vat_registed: any;
  vat_registed?: any;
  purchase_price: number;
  buy_box_price: number;
  fba_fee: number;
  profit: number;
  referral_fee: number;
  roi: number;
  total_fee: number;
  variable_closing_fee: number;
}

export interface SectionLoadingState {
  basic: boolean;
  priceHistory: boolean;
  offers: boolean;
  calculator: boolean;
  gated: boolean;
}

export interface SectionErrorState {
  basic: string | null;
  priceHistory: string | null;
  offers: string | null;
  calculator: string | null;
  gated: string | null;
}
