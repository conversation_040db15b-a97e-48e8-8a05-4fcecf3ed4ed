// cookie.ts

// <PERSON><PERSON> names
export const AUTH_COOKIES = {
  ACCESS_TOKEN: "clickbuy_access_token",
  REFRESH_TOKEN: "clickbuy_refresh_token",
  USER: "clickbuy_user",
};

/**
 * Set a cookie in the browser with proper options
 */
export function setCookie(name: string, value: string, days = 7): void {
  try {
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    const expires = "; expires=" + date.toUTCString();

    // Set secure and SameSite attributes according to environment
    const isLocalhost =
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1";
    const secureFlag = !isLocalhost ? "; Secure" : "";
    const sameSite = isLocalhost ? "Lax" : "None";

    document.cookie =
      name +
      "=" +
      encodeURIComponent(value) +
      expires +
      "; path=/; SameSite=" +
      sameSite +
      secureFlag;

    //console.log(`<PERSON><PERSON> ${name} set successfully`);
  } catch (err) {
    console.error("Error setting cookie:", err);
  }
}

/**
 * Get a cookie value by name
 */
export function getCookie(name: string): string | null {
  try {
    const nameEQ = name + "=";
    const ca = document.cookie.split(";");

    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === " ") c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        const value = decodeURIComponent(c.substring(nameEQ.length, c.length));
        //console.log(`Cookie ${name} retrieved: ${value.substring(0, 10)}...`);
        return value;
      }
    }

    //console.log(`Cookie ${name} not found`);
    return null;
  } catch (err) {
    console.error("Error getting cookie:", err);
    return null;
  }
}

/**
 * Remove a cookie by name
 */
export function removeCookie(name: string): void {
  try {
    // Need to use the same path and domain as when the cookie was set
    const isLocalhost =
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1";
    const secureFlag = !isLocalhost ? "; Secure" : "";
    const sameSite = isLocalhost ? "Lax" : "None";

    document.cookie =
      name + "=; Max-Age=-99999999; path=/; SameSite=" + sameSite + secureFlag;
    //console.log(`Cookie ${name} removed`);
  } catch (err) {
    console.error("Error removing cookie:", err);
  }
}

/**
 * Alternative local storage approach for environments where cookies aren't working well
 */
export function setStorageItem(key: string, value: string): void {
  try {
    localStorage.setItem(key, value);
    //console.log(`LocalStorage ${key} set successfully`);
  } catch (err) {
    console.error("Error setting localStorage item:", err);
  }
}

export function getStorageItem(key: string): string | null {
  try {
    const value = localStorage.getItem(key);
    //console.log(`LocalStorage ${key} ${value ? "retrieved" : "not found"}`);
    return value;
  } catch (err) {
    console.error("Error getting localStorage item:", err);
    return null;
  }
}

export function removeStorageItem(key: string): void {
  try {
    localStorage.removeItem(key);
    //console.log(`LocalStorage ${key} removed`);
  } catch (err) {
    console.error("Error removing localStorage item:", err);
  }
}
