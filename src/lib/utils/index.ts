import React from "react";
import { createRoot } from "react-dom/client";
import { ProductData } from "../types/home";
import { storageService } from "../api/storage";
import { AUTH_COOKIES } from "./cookie";

// src/lib/utils/index.ts
export function formatNumber(value: number | undefined): string {
  if (value === undefined) return "";
  return new Intl.NumberFormat().format(value);
}

export function formatToCurrency(
  value: number | undefined,
  locale = "UK",
): string {
  if (value === undefined) return "";

  const currencySymbol = locale === "UK" ? "£" : "$";
  return `${currencySymbol}${value.toFixed(2)}`;
}

export const getCurrencySymbol = (country: string): string => {
  switch (country) {
    case "US":
      return "$";
    case "GB":
      return "£";
    default:
      return "£";
  }
};

export const formatCurrency = (
  value: number | undefined | null,
  country: string,
) => {
  if (value === undefined || value === null)
    return `${getCurrencySymbol(country)}0.00`;

  try {
    const numValue = Number(value);
    if (isNaN(numValue)) return `${getCurrencySymbol(country)}0.00`;

    return `${getCurrencySymbol(country)}${numValue.toFixed(2)}`;
  } catch (error) {
    console.error("Error formatting currency:", error);
    return `${getCurrencySymbol(country)}0.00`;
  }
};

export const formatWithCommas = (value: number | null | undefined): string => {
  if (value == null) return "";
  const num = Number(value);
  if (isNaN(num)) return "";
  return num.toLocaleString("en-US");
};

export const getCurrentROI = ({
  isVatRegistered,
  productData,
}: {
  isVatRegistered: boolean;
  productData: ProductData;
}) => {
  if (!productData) return 0;
  if (
    isVatRegistered &&
    productData &&
    productData.pricing &&
    productData.pricing.vat_pricing &&
    productData.pricing.vat_pricing.roi !== undefined
  ) {
    return productData.pricing.vat_pricing.roi;
  }
  return Number(
    (productData &&
      productData.pricing &&
      productData.pricing.non_vat_pricing.roi) ||
      0,
  );
};

export const getCurrentProfit = ({
  isVatRegistered,
  productData,
}: {
  isVatRegistered: boolean;
  productData: ProductData;
}) => {
  if (!productData) return 0;
  if (
    isVatRegistered &&
    productData &&
    productData.pricing &&
    productData.pricing.vat_pricing &&
    productData.pricing.vat_pricing.profit !== undefined
  ) {
    return productData.pricing.vat_pricing.profit;
  }
  return (
    Number(
      productData &&
        productData.pricing &&
        productData.pricing.non_vat_pricing.profit,
    ) || 0
  );
};

export const getCurrentBuyBoxPrice = ({
  isVatRegistered,
  productData,
}: {
  isVatRegistered: boolean;
  productData: ProductData;
}) => {
  if (!productData) return 0;
  if (
    isVatRegistered &&
    productData &&
    productData.pricing &&
    productData.pricing.vat_pricing &&
    productData.pricing.vat_pricing.buy_box_price !== undefined
  ) {
    return productData.pricing.vat_pricing.buy_box_price;
  }
  return (
    Number(
      productData &&
        productData.pricing &&
        productData.pricing.non_vat_pricing.buy_box_price,
    ) || 0
  );
};

export const getCurrentFBAFees = ({
  isVatRegistered,
  productData,
}: {
  isVatRegistered: boolean;
  productData: ProductData;
}) => {
  if (!productData) return 0;
  if (
    isVatRegistered &&
    productData &&
    productData.pricing &&
    productData.pricing.vat_pricing &&
    productData.pricing.vat_pricing.fba_fee !== undefined
  ) {
    return productData.pricing.vat_pricing.fba_fee;
  }
  return (
    Number(
      productData &&
        productData.pricing &&
        productData.pricing.non_vat_pricing.fba_fee,
    ) || 0
  );
};

export const getCurrentReferralFee = ({
  isVatRegistered,
  productData,
}: {
  isVatRegistered: boolean;
  productData: ProductData;
}) => {
  if (!productData) return 0;
  if (
    isVatRegistered &&
    productData &&
    productData.pricing &&
    productData.pricing.vat_pricing &&
    productData.pricing.vat_pricing.referral_fee !== undefined
  ) {
    return productData.pricing.vat_pricing.referral_fee;
  }
  return (
    Number(
      productData &&
        productData.pricing &&
        productData.pricing.non_vat_pricing.referral_fee,
    ) || 0
  );
};

export const getCurrentTotalFees = ({
  isVatRegistered,
  productData,
}: {
  isVatRegistered: boolean;
  productData: ProductData;
}) => {
  if (!productData) return 0;
  if (
    isVatRegistered &&
    productData &&
    productData.pricing &&
    productData.pricing.vat_pricing &&
    productData.pricing.vat_pricing.total_fee !== undefined
  ) {
    return productData.pricing.vat_pricing.total_fee;
  }
  return (
    Number(
      productData &&
        productData.pricing &&
        productData.pricing.non_vat_pricing.total_fee,
    ) || 0
  );
};

export const formatEstimatedSales = (sales: number): string => {
  return sales === 0 ? "N/A" : `${sales.toLocaleString()}/mo`;
};

export function getAccessToken(): string | null {
  const token = storageService.getItem(AUTH_COOKIES.ACCESS_TOKEN);
  return token;
}

export const getGlobalESearchParams = (
  selectedCountry: "US" | "GB" | "DE" | "FR" | "IT" | "ES" | "JP" | "CA" | "IN",
) => {
  // Map countries to their marketplace IDs in GlobaleSearch
  const marketplaceMap: { [key: string]: number } = {
    US: 0,
    GB: 3,
    DE: 1,
    FR: 2,
    IT: 4,
    ES: 5,
    JP: 6,
    CA: 7,
    IN: 8,
  };

  return marketplaceMap[selectedCountry] !== undefined
    ? marketplaceMap[selectedCountry]
    : 3; // Default to UK (3)
};
