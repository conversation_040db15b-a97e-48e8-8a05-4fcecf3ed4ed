// Basic type for messages
export type MessageType =
  | "getProductData" // Request product data from current page
  | "productData" // Receive product data
  | "switchPosition" // Switch panel position (left/right)
  | "toggleVisibility" // Show/hide panel
  | "setWidth"; // Change panel width

export interface ExtensionMessage {
  type: MessageType;
  data?: any;
  width?: number;
}

/**
 * Send message to parent extension
 */
export const sendMessageToExtension = (message: ExtensionMessage): void => {
  if (typeof window !== "undefined" && window.parent) {
    window.parent.postMessage(message, "*");
  }
};

/**
 * Add listener for messages from extension
 */
export const addExtensionMessageListener = (
  callback: (message: ExtensionMessage) => void,
): (() => void) => {
  if (typeof window === "undefined") return () => {};

  const handler = (event: MessageEvent) => {
    if (event.data && typeof event.data === "object") {
      callback(event.data as ExtensionMessage);
    }
  };

  window.addEventListener("message", handler);

  // Return cleanup function
  return () => window.removeEventListener("message", handler);
};
